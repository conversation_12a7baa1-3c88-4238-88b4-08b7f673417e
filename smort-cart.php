<?php

/**
 * Plugin Name: Smort Cart
 * Description: Varukorg meny
 * Version: 1.0
 * Author: Smort
 */

// Exit if accessed directly
if (!defined('ABSPATH')) exit;

// Include admin functionality
require_once plugin_dir_path(__FILE__) . 'admin-smort-cart.php';

class smort_Cart
{
    private $options;

    public function __construct()
    {
        // Load options
        $this->options = get_option('smort_cart_options');
        if (!$this->options) {
            $this->options = array(
                'cart_style' => 'default',
                'primary_color' => '#000000',
                'cart_theme' => 'modern',
                'header_bg_color' => '#ffffff',
                'header_text_color' => '#000000',
                'custom_svg' => '',
                'button_radius' => '0',
                'button_bg_color' => '#000000',
                'button_text_color' => '#ffffff',
                'use_logo' => 'no',
                'header_logo' => '',
                'notice_bg_color' => '#f7f6f7',
                'notice_text_color' => '#515151',
                'notice_border' => 'yes',
                'enable_upsells' => 'yes',
                'upsell_rules' => array()
            );
        }

        // Register scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Register shortcode for cart icon
        add_shortcode('smort_cart', array($this, 'smort_cart_shortcode'));

        // Ajax handlers for cart updates
        add_action('wp_ajax_update_smort_cart', array($this, 'update_cart_ajax'));
        add_action('wp_ajax_nopriv_update_smort_cart', array($this, 'update_cart_ajax'));

        // Add custom CSS for WooCommerce notices
        add_action('wp_head', array($this, 'add_notice_styles'));

        // Ajax handler for adding upsell products to cart
        add_action('wp_ajax_add_upsell_to_cart', array($this, 'add_upsell_to_cart_ajax'));
        add_action('wp_ajax_nopriv_add_upsell_to_cart', array($this, 'add_upsell_to_cart_ajax'));
    }

    // Shortcode function
    public function smort_cart_shortcode()
    {
        ob_start();
        $this->display_cart_icon();
        return ob_get_clean();
    }
    public function enqueue_scripts()
    {
        wp_enqueue_style('smort-cart-css', plugin_dir_url(__FILE__) . 'css/smort-cart.css');
        wp_enqueue_script('smort-cart-js', plugin_dir_url(__FILE__) . 'js/smort-cart.js', array('jquery'), '1.0', true);

        wp_localize_script('smort-cart-js', 'smort_cart_vars', array(
            'ajax_url' => admin_url('admin-ajax.php')
        ));
    }

    public function display_cart_icon()
    {
        // Get cart contents if WooCommerce is active
        $cart_count = 0;
        if (function_exists('WC')) {
            $cart_count = WC()->cart->get_cart_contents_count();
        }

        // Get all options with defaults
        $cart_style = isset($this->options['cart_style']) ? $this->options['cart_style'] : 'default';
        $cart_theme = isset($this->options['cart_theme']) ? $this->options['cart_theme'] : 'modern';
        $custom_svg = isset($this->options['custom_svg']) ? $this->options['custom_svg'] : '';
        $primary_color = isset($this->options['primary_color']) ? $this->options['primary_color'] : '#000000';
        $header_bg_color = isset($this->options['header_bg_color']) ? $this->options['header_bg_color'] : '#ffffff';
        $header_text_color = isset($this->options['header_text_color']) ? $this->options['header_text_color'] : '#000000';
        $button_radius = isset($this->options['button_radius']) ? intval($this->options['button_radius']) : 0;
        $button_bg_color = isset($this->options['button_bg_color']) ? $this->options['button_bg_color'] : '#000000';
        $button_text_color = isset($this->options['button_text_color']) ? $this->options['button_text_color'] : '#ffffff';
        $use_logo = isset($this->options['use_logo']) ? $this->options['use_logo'] : 'no';
        $header_logo = isset($this->options['header_logo']) ? $this->options['header_logo'] : '';

        // Output HTML for smort cart
?>
        <div id="smort-cart-container" class="theme-<?php echo esc_attr($cart_theme); ?>">
            <div class="smort-cart-icon">
                <?php
                // Display cart icon based on settings
                if ($cart_style === 'custom' && !empty($custom_svg)) {
                    // Use custom SVG from settings
                    echo '<img src="' . esc_url($custom_svg) . '" alt="Cart" class="cart-icon" style="color: ' . esc_attr($primary_color) . ';">';
                } else {
                    // Use predefined SVG based on style
                    $icon_file = 'cart.svg'; // Default

                    if ($cart_style === 'minimal') {
                        $icon_file = 'cart-minimal.svg';
                    } elseif ($cart_style === 'rounded') {
                        $icon_file = 'cart-rounded.svg';
                    } elseif ($cart_style === 'modern') {
                        $icon_file = 'cart-modern.svg';
                    } elseif ($cart_style === 'cart-outline') {
                        $icon_file = 'cart-outline.svg';
                    } elseif ($cart_style === 'bag-outline') {
                        $icon_file = 'bag-outline.svg';
                    }

                    // Check if the file exists in child theme first
                    $child_theme_icon_path = get_stylesheet_directory() . '/img/' . $icon_file;
                    $child_theme_icon = get_stylesheet_directory_uri() . '/img/' . $icon_file;
                    $default_icon = plugin_dir_url(__FILE__) . 'img/' . $icon_file;

                    if (file_exists($child_theme_icon_path)) {
                        echo '<img src="' . esc_url($child_theme_icon) . '" alt="Cart" class="cart-icon" style="color: ' . esc_attr($primary_color) . ';">';
                    } elseif (file_exists(plugin_dir_path(__FILE__) . 'img/' . $icon_file)) {
                        echo '<img src="' . esc_url($default_icon) . '" alt="Cart" class="cart-icon" style="color: ' . esc_attr($primary_color) . ';">';
                    } else {
                        // Fallback to Font Awesome icon
                        echo '<i class="fas cart-icon fa-shopping-cart" style="color: ' . esc_attr($primary_color) . ';"></i>';
                    }
                }
                ?>

                <?php if ($cart_count > 0) : ?>
                    <span class="cart-count" style="background-color: <?php echo esc_attr($primary_color); ?>;"><?php echo $cart_count; ?></span>
                <?php endif; ?>
            </div>
            <div class="smort-cart-content">
                <div>
                    <?php
                    // Set header styles based on theme
                    $header_style = "background-color: {$header_bg_color}; color: {$header_text_color};";

                    if ($cart_theme === 'futuristic') {
                        $header_style .= " background: linear-gradient(135deg, {$header_bg_color} 0%, rgba(0,0,0,0.1) 100%);";
                    }
                    ?>
                    <div class="cart-header" style="<?php echo esc_attr($header_style); ?>">
                        <?php
                        if ($use_logo === 'yes' && !empty($header_logo)) {
                            echo '<div class="header-logo"><img src="' . esc_url($header_logo) . '" alt="Logo" /></div>';
                        } else {
                            echo '<h3>Din varukorg</h3>';
                        }
                        ?>
                        <span class="close-cart" style="background-color: <?php echo esc_attr($primary_color); ?>;">×</span>
                    </div>
                    <div class="cart-items">
                        <?php $this->get_cart_contents(); ?>
                    </div>
                </div>
                <?php
                // Display upsell products if enabled
                $enable_upsells = isset($this->options['enable_upsells']) ? $this->options['enable_upsells'] : 'yes';
                ?>

                <div class="cart-footer">
                    <?php
                    // Visa uppförsäljningsprodukter precis ovanför totalsumman
                    if ($enable_upsells === 'yes') {
                        $this->display_upsell_products();
                    }
                    ?>

                    <div class="cart-total" style="background-color: #f9f9f9; padding: 10px; border-radius: 4px; margin-bottom: 10px;">
                        <?php
                        if (function_exists('WC')) {
                            echo '<p style="font-size: 16px; margin: 0;">Totalsumma: <strong style="font-size: 18px;">' . $this->get_cart_total_with_tax_toggle() . '</strong></p>';
                        }
                        ?>
                    </div>
                    <?php
                    // Set checkout button styles based on theme
                    $checkout_btn_style = "border-radius: {$button_radius}px; background-color: {$button_bg_color}; color: {$button_text_color};";

                    if ($cart_theme === 'futuristic') {
                        $checkout_btn_style .= " background: linear-gradient(135deg, {$button_bg_color} 0%, rgba(0,0,0,0.2) 100%);";
                        $checkout_btn_style .= " box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);";
                    }
                    ?>
                    <a href="<?php echo wc_get_checkout_url(); ?>" class="checkout-btn" style="<?php echo esc_attr($checkout_btn_style); ?>">Till kassan</a>
                </div>
            </div>
        </div>
        <?php
    }

    public function get_cart_contents()
    {
        if (!function_exists('WC')) {
            echo '<p>WooCommerce är inte aktiverat.</p>';
            return;
        }

        $cart_items = WC()->cart->get_cart();

        if (empty($cart_items)) {
            echo '<p>Din varukorg är tom.</p>';
            return;
        }

        // Get styling options
        $primary_color = isset($this->options['primary_color']) ? $this->options['primary_color'] : '#000000';
        $cart_theme = isset($this->options['cart_theme']) ? $this->options['cart_theme'] : 'modern';

        foreach ($cart_items as $cart_item_key => $cart_item) {
            $product = $cart_item['data'];
            $product_name = $product->get_name();
            $quantity = $cart_item['quantity'];

            // Check tax toggle setting and get appropriate price
            $show_excl_tax = isset($_COOKIE['smort_tax_toggle']) && $_COOKIE['smort_tax_toggle'] === 'excl';
            if ($show_excl_tax) {
                $unit_price = wc_get_price_excluding_tax($product);
                $tax_label = ' <span class="tax-label" style="font-size: 11px; color: #666;">exkl moms</span>';
            } else {
                $unit_price = wc_get_price_including_tax($product);
                $tax_label = ' <span class="tax-label" style="font-size: 11px; color: #666;">inkl moms</span>';
            }

            $price = wc_price($unit_price) . $tax_label;

            // Hämta produktkategorier
            $categories = '';
            if ($product->is_type('variation')) {
                $parent_id = $product->get_parent_id();
                $parent_product = wc_get_product($parent_id);
                $category_ids = $parent_product->get_category_ids();
            } else {
                $category_ids = $product->get_category_ids();
            }

            if (!empty($category_ids)) {
                $first_category = get_term($category_ids[0], 'product_cat');
                if ($first_category && !is_wp_error($first_category)) {
                    $categories = $first_category->name;
                }
            }
        ?>
            <div class="cart-item" data-key="<?php echo $cart_item_key; ?>">
                <div class="item-image">
                    <?php echo $product->get_image('thumbnail'); ?>
                </div>
                <div class="item-details" style="color: #333; display: flex; flex-direction: column; justify-content: space-between; height: 100%;">
                    <div class="item-info">
                        <h4 style="color: #333; margin: 0 0 2px 0; font-size: 15px; line-height: 1.3;">
                            <a href="<?php echo esc_url(get_permalink($product->get_id())); ?>" style="color: #333; text-decoration: none; font-weight: bold;">
                                <?php echo $product_name; ?>
                            </a>
                        </h4>
                        <?php if (!empty($categories)) : ?>
                            <p class="item-category" style="color: #999; margin: 3px 0 0 0; font-size: 11px;"><?php echo $categories; ?></p>
                        <?php endif; ?>
                    </div>

                    <div class="item-quantity-price" style="margin-top: auto;">
                        <div class="quantity-controls" style="border-radius: 4px; overflow: hidden;">
                            <?php
                            // Set quantity button styles based on theme
                            $quantity_btn_style = "color: {$primary_color};";

                            if ($cart_theme === 'futuristic') {
                                $quantity_btn_style .= " background-color: white; border-radius: 50%; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);";
                            } elseif ($cart_theme === 'modern') {
                                $quantity_btn_style .= " background-color: transparent;";
                            }
                            ?>
                            <button type="button" class="quantity-btn minus" style="<?php echo esc_attr($quantity_btn_style); ?>">-</button>
                            <input type="number" readonly class="quantity-input" value="<?php echo $quantity; ?>" min="1" data-key="<?php echo $cart_item_key; ?>" style="text-align: center; line-height: 24px; padding: 0; margin: 0;">
                            <button type="button" class="quantity-btn plus" style="<?php echo esc_attr($quantity_btn_style); ?>">+</button>
                        </div>

                        <div class="item-price-container" style="color: #333; text-align: right;">
                            <?php if ($quantity > 1) : ?>
                                <div style="white-space: nowrap; display: flex; align-items: center; justify-content: flex-end; gap: 8px;">
                                    <div style="font-size: 12px; color: #666;">
                                        <?php echo $quantity; ?> × <?php echo $price; ?>
                                    </div>
                                    <div style="font-weight: bold; font-size: 16px;">
                                        <?php echo wc_price($quantity * $unit_price) . $tax_label; ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div style="font-weight: bold; font-size: 16px;">
                                    <?php echo $price; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="item-remove">
                    <a href="#" class="remove-item" data-key="<?php echo $cart_item_key; ?>" style="background-color: <?php echo esc_attr($primary_color); ?>;">×</a>
                </div>
            </div>
        <?php
        }
    }

    /**
     * Add custom styles for WooCommerce notices
     */
    public function add_notice_styles()
    {
        $notice_bg_color = isset($this->options['notice_bg_color']) ? $this->options['notice_bg_color'] : '#f7f6f7';
        $notice_text_color = isset($this->options['notice_text_color']) ? $this->options['notice_text_color'] : '#515151';
        $notice_border = isset($this->options['notice_border']) ? $this->options['notice_border'] : 'yes';

        $border_style = $notice_border === 'yes' ? 'border-left: 4px solid #00a32a;' : 'border-left: none;';

        echo '<style>
            .woocommerce-message,
            .woocommerce-info,
            .woocommerce-error,
            .woocommerce-notice {
                background-color: ' . esc_attr($notice_bg_color) . ' !important;
                color: ' . esc_attr($notice_text_color) . ' !important;
                ' . $border_style . '
                padding: 1em 1.5em !important;
                margin: 0 0 2em !important;
                border-top: none !important;
                border-right: none !important;
                border-bottom: none !important;
                border-radius: 0 !important;
            }

            /* Hide the default icon */
            .woocommerce-message::before,
            .woocommerce-info::before,
            .woocommerce-error::before,
            .woocommerce-notice::before {
                display: none !important;
            }

            /* Adjust padding if border is removed */
            ' . ($notice_border === 'no' ? '
            .woocommerce-message,
            .woocommerce-info,
            .woocommerce-error,
            .woocommerce-notice {
                padding-left: 1.5em !important;
            }
            ' : '') . '
        </style>';
    }

    /**
     * Display upsell products based on cart contents
     */
    public function display_upsell_products()
    {
        if (!function_exists('WC') || empty($this->options['upsell_rules'])) {
            return;
        }

        $cart_items = WC()->cart->get_cart();
        $cart_product_ids = array();
        $cart_category_ids = array();

        // Get all product IDs and category IDs in cart
        foreach ($cart_items as $cart_item) {
            $product = $cart_item['data'];
            $product_id = $product->get_id();
            $cart_product_ids[] = $product_id;

            // Get parent ID for variations
            if ($product->is_type('variation')) {
                $parent_id = $product->get_parent_id();
                $cart_product_ids[] = $parent_id;

                // Get categories from parent product
                $parent_product = wc_get_product($parent_id);
                if ($parent_product) {
                    $category_ids = $parent_product->get_category_ids();
                    $cart_category_ids = array_merge($cart_category_ids, $category_ids);
                }
            } else {
                // Get categories directly
                $category_ids = $product->get_category_ids();
                $cart_category_ids = array_merge($cart_category_ids, $category_ids);
            }
        }

        // Remove duplicates
        $cart_product_ids = array_unique($cart_product_ids);
        $cart_category_ids = array_unique($cart_category_ids);

        $upsell_products = array();

        // Check each rule
        foreach ($this->options['upsell_rules'] as $rule) {
            if (empty($rule['trigger_id']) || empty($rule['upsell_id'])) {
                continue;
            }

            $trigger_id = $rule['trigger_id'];
            $upsell_id = $rule['upsell_id'];
            $type = isset($rule['type']) ? $rule['type'] : 'product';

            // Skip if upsell product is already in cart
            if (in_array($upsell_id, $cart_product_ids)) {
                continue;
            }

            // Check if rule matches
            $rule_matches = false;

            if ($type === 'product' && in_array($trigger_id, $cart_product_ids)) {
                $rule_matches = true;
            } elseif ($type === 'category' && in_array($trigger_id, $cart_category_ids)) {
                $rule_matches = true;
            }

            if ($rule_matches) {
                $upsell_product = wc_get_product($upsell_id);
                if ($upsell_product && $upsell_product->is_purchasable() && $upsell_product->is_in_stock()) {
                    $upsell_products[$upsell_id] = $upsell_product;
                }
            }
        }

        // Display upsell products if any
        if (!empty($upsell_products)) {
            $primary_color = isset($this->options['primary_color']) ? $this->options['primary_color'] : '#000000';
        ?>
            <div class="cart-upsells">
                <h4>Komplettera din beställning</h4>
                <div class="upsell-products">
                    <?php foreach ($upsell_products as $product_id => $product) : ?>
                        <div class="upsell-product">
                            <div class="upsell-product-info">
                                <a href="<?php echo esc_url(get_permalink($product_id)); ?>">
                                    <?php echo $product->get_image('thumbnail'); ?>
                                </a>
                                <div class="upsell-product-details">
                                    <h5><a href="<?php echo esc_url(get_permalink($product_id)); ?>"><?php echo $product->get_name(); ?></a></h5>
                                    <span class="price"><?php echo $this->get_product_price_with_tax_toggle($product); ?></span>
                                </div>
                            </div>
                            <div class="upsell-product-actions">
                                <button type="button" class="add-to-cart-btn add-upsell-to-cart" data-product-id="<?php echo esc_attr($product_id); ?>" style="background-color: <?php echo esc_attr($primary_color); ?>;">
                                    Lägg till
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
<?php
        }
    }

    /**
     * AJAX handler for adding upsell products to cart
     */
    public function add_upsell_to_cart_ajax()
    {
        // Check if WooCommerce is active
        if (!function_exists('WC')) {
            wp_send_json_error('WooCommerce is not active');
            wp_die();
        }

        // Check if product ID is set
        if (!isset($_POST['product_id'])) {
            wp_send_json_error('No product ID provided');
            wp_die();
        }

        $product_id = absint($_POST['product_id']);
        $product = wc_get_product($product_id);

        if (!$product) {
            wp_send_json_error('Invalid product');
            wp_die();
        }

        // Add product to cart
        $cart_item_key = WC()->cart->add_to_cart($product_id, 1);

        if ($cart_item_key) {
            wp_send_json_success(array(
                'message' => $product->get_name() . ' har lagts till i din varukorg.',
                'cart_count' => WC()->cart->get_cart_contents_count(),
                'cart_item_key' => $cart_item_key
            ));
        } else {
            wp_send_json_error('Failed to add product to cart');
        }

        wp_die();
    }

    public function update_cart_ajax()
    {
        // Check if WooCommerce is active
        if (!function_exists('WC')) {
            wp_send_json_error('WooCommerce is not active');
            wp_die();
        }

        // Handle remove item request
        if (isset($_POST['remove_item']) && isset($_POST['cart_item_key'])) {
            $cart_item_key = sanitize_text_field($_POST['cart_item_key']);
            WC()->cart->remove_cart_item($cart_item_key);
            wp_send_json_success();
        }

        // Handle get count request
        if (isset($_POST['get_count'])) {
            echo WC()->cart->get_cart_contents_count();
            wp_die();
        }

        // Handle refresh cart request
        if (isset($_POST['refresh_cart'])) {
            $this->get_cart_contents();
            wp_die();
        }

        // Handle get total request
        if (isset($_POST['get_total'])) {
            echo '<p style="font-size: 16px; margin: 0;">Totalsumma: <strong style="font-size: 18px;">' . $this->get_cart_total_with_tax_toggle() . '</strong></p>';
            wp_die();
        }

        // Handle quantity update
        if (isset($_POST['quantity']) && isset($_POST['cart_item_key'])) {
            $cart_item_key = sanitize_text_field($_POST['cart_item_key']);
            $quantity = intval($_POST['quantity']);
            WC()->cart->set_quantity($cart_item_key, $quantity);
            wp_send_json_success();
        }

        wp_die();
    }

    /**
     * Get cart total with tax toggle consideration
     */
    private function get_cart_total_with_tax_toggle()
    {
        if (!function_exists('WC')) {
            return '';
        }

        $show_excl_tax = isset($_COOKIE['smort_tax_toggle']) && $_COOKIE['smort_tax_toggle'] === 'excl';
        $cart_items = WC()->cart->get_cart();
        $total = 0;

        foreach ($cart_items as $cart_item) {
            $product = $cart_item['data'];
            $quantity = $cart_item['quantity'];

            if ($show_excl_tax) {
                $item_price = wc_get_price_excluding_tax($product);
            } else {
                $item_price = wc_get_price_including_tax($product);
            }

            $total += $item_price * $quantity;
        }

        $tax_label = $show_excl_tax ? ' <span style="font-size: 12px; color: #666;">exkl moms</span>' : ' <span style="font-size: 12px; color: #666;">inkl moms</span>';

        return wc_price($total) . $tax_label;
    }

    /**
     * Get product price with tax toggle consideration
     */
    private function get_product_price_with_tax_toggle($product)
    {
        $show_excl_tax = isset($_COOKIE['smort_tax_toggle']) && $_COOKIE['smort_tax_toggle'] === 'excl';
        $tax_label = $show_excl_tax ? ' <span class="tax-label" style="font-size: 11px; color: #666;">exkl moms</span>' : ' <span class="tax-label" style="font-size: 11px; color: #666;">inkl moms</span>';

        // Check if product has sale price
        $regular_price_raw = $product->get_regular_price();
        $sale_price_raw = $product->get_sale_price();

        if ($product->is_on_sale() && !empty($sale_price_raw)) {
            // Product is on sale - show both regular and sale price
            if ($show_excl_tax) {
                $regular_price = wc_get_price_excluding_tax($product, array('price' => $regular_price_raw));
                $sale_price = wc_get_price_excluding_tax($product, array('price' => $sale_price_raw));
            } else {
                $regular_price = wc_get_price_including_tax($product, array('price' => $regular_price_raw));
                $sale_price = wc_get_price_including_tax($product, array('price' => $sale_price_raw));
            }

            $price_html = '<del aria-hidden="true">' . wc_price($regular_price) . '</del> ';
            $price_html .= '<ins>' . wc_price($sale_price) . '</ins>';
            $price_html .= $tax_label;

            return $price_html;
        } else {
            // Regular price only
            if ($show_excl_tax) {
                $price = wc_get_price_excluding_tax($product);
            } else {
                $price = wc_get_price_including_tax($product);
            }
            return wc_price($price) . $tax_label;
        }
    }
}

// Initialize the plugin
new smort_Cart();
