<?php
// Exit if accessed directly
if (!defined('ABSPATH')) exit;

class Smort_Cart_Admin {
    private $options;

    public function __construct() {
        // Add menu item
        add_action('admin_menu', array($this, 'add_plugin_page'));

        // Initialize settings
        add_action('admin_init', array($this, 'page_init'));

        // Add settings link on plugin page
        add_filter('plugin_action_links_smort-cart/smort-cart.php', array($this, 'add_settings_link'));

        // Add AJAX handlers for product and category search
        add_action('wp_ajax_smort_cart_search_products', array($this, 'ajax_search_products'));
        add_action('wp_ajax_smort_cart_search_categories', array($this, 'ajax_search_categories'));

        // Enqueue admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Load options
        $this->options = get_option('smort_cart_options');
        if (!$this->options) {
            $this->options = $this->get_default_options();
            update_option('smort_cart_options', $this->options);
        }

        // We don't need CSS variables anymore since we're using inline styles
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on our settings page
        if ($hook != 'settings_page_smort-cart-settings') {
            return;
        }

        // Debug info
        error_log('Smort Cart: Loading admin scripts on hook: ' . $hook);

        // Enqueue Select2 from WordPress core if available (WooCommerce often includes it)
        if (!wp_script_is('select2', 'registered')) {
            wp_register_style('select2', 'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css', array(), '4.0.13');
            wp_register_script('select2', 'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js', array('jquery'), '4.0.13', true);
        }

        wp_enqueue_style('select2');
        wp_enqueue_script('select2');

        // Add our custom admin script - use a version with timestamp to prevent caching
        $version = '1.0.1-' . time();
        wp_enqueue_script('smort-cart-admin', plugin_dir_url(__FILE__) . 'js/admin-smort-cart.js', array('jquery', 'select2'), $version, true);

        // Pass data to our script
        wp_localize_script('smort-cart-admin', 'smort_cart_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'product_nonce' => wp_create_nonce('smort_cart_search_products'),
            'category_nonce' => wp_create_nonce('smort_cart_search_categories'),
            'debug' => true
        ));
    }

    /**
     * Get default options
     */
    private function get_default_options() {
        return array(
            'cart_style' => 'default',
            'primary_color' => '#000000',
            'cart_theme' => 'modern',
            'header_bg_color' => '#ffffff',
            'header_text_color' => '#000000',
            'custom_svg' => '',
            'button_radius' => '0',
            'button_bg_color' => '#000000',
            'button_text_color' => '#ffffff',
            'use_logo' => 'no',
            'header_logo' => '',
            'notice_bg_color' => '#f7f6f7',
            'notice_text_color' => '#515151',
            'notice_border' => 'yes',
            'enable_upsells' => 'yes',
            'upsell_rules' => array()
        );
    }

    /**
     * Add settings link to plugin page
     */
    public function add_settings_link($links) {
        $settings_link = '<a href="admin.php?page=smort-cart-settings">' . __('Inställningar') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }

    /**
     * Add options page
     */
    public function add_plugin_page() {
        add_menu_page(
            'Smort varukorg',
            'Smort varukorg',
            'manage_options',
            'smort-cart-settings',
            array($this, 'create_admin_page'),
            'dashicons-cart',
            58
        );
    }

    // We don't need the add_custom_css_variables method anymore since we're using inline styles

    /**
     * Options page callback
     */
    public function create_admin_page() {
        ?>
        <div class="wrap">
            <h1>Smort varukorg inställningar</h1>
            <form method="post" action="options.php" enctype="multipart/form-data">
            <?php
                settings_fields('smort_cart_option_group');
                do_settings_sections('smort-cart-settings');
                submit_button();
            ?>
            </form>
        </div>
        <?php
    }

    /**
     * Register and add settings
     */
    public function page_init() {
        register_setting(
            'smort_cart_option_group', // Option group
            'smort_cart_options', // Option name
            array($this, 'sanitize') // Sanitize
        );

        add_settings_section(
            'smort_cart_setting_section', // ID
            'Varukorg inställningar', // Title
            array($this, 'print_section_info'), // Callback
            'smort-cart-settings' // Page
        );

        add_settings_field(
            'cart_style', // ID
            'Varukorg stil', // Title
            array($this, 'cart_style_callback'), // Callback
            'smort-cart-settings', // Page
            'smort_cart_setting_section' // Section
        );

        add_settings_field(
            'custom_svg',
            'Ladda upp egen SVG ikon',
            array($this, 'custom_svg_callback'),
            'smort-cart-settings',
            'smort_cart_setting_section'
        );

        add_settings_field(
            'primary_color',
            'Primär färg',
            array($this, 'primary_color_callback'),
            'smort-cart-settings',
            'smort_cart_setting_section'
        );

        add_settings_field(
            'cart_theme',
            'Varukorg tema',
            array($this, 'cart_theme_callback'),
            'smort-cart-settings',
            'smort_cart_setting_section'
        );

        add_settings_field(
            'header_bg_color',
            'Header bakgrundsfärg',
            array($this, 'header_bg_color_callback'),
            'smort-cart-settings',
            'smort_cart_setting_section'
        );

        add_settings_field(
            'header_text_color',
            'Header textfärg',
            array($this, 'header_text_color_callback'),
            'smort-cart-settings',
            'smort_cart_setting_section'
        );

        add_settings_field(
            'button_settings',
            'Knappinställningar',
            array($this, 'button_settings_callback'),
            'smort-cart-settings',
            'smort_cart_setting_section'
        );

        add_settings_field(
            'header_logo',
            'Header logotyp',
            array($this, 'header_logo_callback'),
            'smort-cart-settings',
            'smort_cart_setting_section'
        );

        add_settings_field(
            'notice_settings',
            'Meddelande-inställningar',
            array($this, 'notice_settings_callback'),
            'smort-cart-settings',
            'smort_cart_setting_section'
        );

        add_settings_field(
            'upsell_settings',
            'Uppförsäljningsinställningar',
            array($this, 'upsell_settings_callback'),
            'smort-cart-settings',
            'smort_cart_setting_section'
        );
    }

    /**
     * Sanitize each setting field as needed
     */
    public function sanitize($input) {
        $new_input = array();

        if(isset($input['cart_style']))
            $new_input['cart_style'] = sanitize_text_field($input['cart_style']);

        if(isset($input['primary_color']))
            $new_input['primary_color'] = sanitize_text_field($input['primary_color']);

        if(isset($input['cart_theme']))
            $new_input['cart_theme'] = sanitize_text_field($input['cart_theme']);

        if(isset($input['header_bg_color']))
            $new_input['header_bg_color'] = sanitize_text_field($input['header_bg_color']);

        if(isset($input['header_text_color']))
            $new_input['header_text_color'] = sanitize_text_field($input['header_text_color']);

        if(isset($input['custom_svg']))
            $new_input['custom_svg'] = sanitize_text_field($input['custom_svg']);

        if(isset($input['button_radius'])) {
            // Ensure button_radius is a number and convert to string
            $new_input['button_radius'] = (string) absint($input['button_radius']);
        }

        if(isset($input['button_bg_color']))
            $new_input['button_bg_color'] = sanitize_text_field($input['button_bg_color']);

        if(isset($input['button_text_color']))
            $new_input['button_text_color'] = sanitize_text_field($input['button_text_color']);

        if(isset($input['use_logo']))
            $new_input['use_logo'] = sanitize_text_field($input['use_logo']);

        if(isset($input['header_logo']))
            $new_input['header_logo'] = sanitize_text_field($input['header_logo']);

        if(isset($input['notice_bg_color']))
            $new_input['notice_bg_color'] = sanitize_text_field($input['notice_bg_color']);

        if(isset($input['notice_text_color']))
            $new_input['notice_text_color'] = sanitize_text_field($input['notice_text_color']);

        if(isset($input['notice_border']))
            $new_input['notice_border'] = sanitize_text_field($input['notice_border']);

        if(isset($input['enable_upsells']))
            $new_input['enable_upsells'] = sanitize_text_field($input['enable_upsells']);

        if(isset($input['upsell_rules']) && is_array($input['upsell_rules'])) {
            $new_input['upsell_rules'] = array();
            foreach($input['upsell_rules'] as $rule) {
                if(!empty($rule)) {
                    $sanitized_rule = array();

                    if(isset($rule['type']))
                        $sanitized_rule['type'] = sanitize_text_field($rule['type']);

                    if(isset($rule['trigger_id']))
                        $sanitized_rule['trigger_id'] = absint($rule['trigger_id']);

                    if(isset($rule['upsell_id']))
                        $sanitized_rule['upsell_id'] = absint($rule['upsell_id']);

                    $new_input['upsell_rules'][] = $sanitized_rule;
                }
            }
        }

        return $new_input;
    }

    /**
     * Print the Section text
     */
    public function print_section_info() {
        print 'Anpassa din Smort varukorg med inställningarna nedan:';
    }

    /**
     * Cart style callback
     */
    public function cart_style_callback() {
        $cart_style = isset($this->options['cart_style']) ? $this->options['cart_style'] : 'default';
        $plugin_url = plugin_dir_url(__FILE__);
        ?>
        <div class="cart-style-selector">
            <style>
                .cart-style-options {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 15px;
                    margin-bottom: 15px;
                }
                .cart-style-option {
                    border: 2px solid #ddd;
                    border-radius: 5px;
                    padding: 10px;
                    width: 100px;
                    height: 100px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }
                .cart-style-option:hover {
                    border-color: #999;
                }
                .cart-style-option.selected {
                    border-color: #2271b1;
                    background-color: #f0f6fc;
                }
                .cart-style-option svg {
                    width: 40px;
                    height: 40px;
                    margin-bottom: 10px;
                }
                .cart-style-option span {
                    font-size: 12px;
                    text-align: center;
                }
                .cart-style-option svg path,
                .cart-style-option svg circle,
                .cart-style-option svg g {
                    stroke: #333;
                    fill: none;
                }
                .cart-style-option.selected svg path,
                .cart-style-option.selected svg circle,
                .cart-style-option.selected svg g {
                    stroke: #2271b1;
                }
            </style>

            <input type="hidden" name="smort_cart_options[cart_style]" id="cart_style" value="<?php echo esc_attr($cart_style); ?>">

            <div class="cart-style-options">
                <div class="cart-style-option <?php echo $cart_style === 'default' ? 'selected' : ''; ?>" data-value="default">
                    <img src="<?php echo $plugin_url; ?>img/cart.svg" alt="Standard" width="40" height="40">
                    <span>Standard</span>
                </div>

                <div class="cart-style-option <?php echo $cart_style === 'minimal' ? 'selected' : ''; ?>" data-value="minimal">
                    <img src="<?php echo $plugin_url; ?>img/cart-minimal.svg" alt="Minimal" width="40" height="40">
                    <span>Minimal</span>
                </div>

                <div class="cart-style-option <?php echo $cart_style === 'rounded' ? 'selected' : ''; ?>" data-value="rounded">
                    <img src="<?php echo $plugin_url; ?>img/cart-rounded.svg" alt="Rundad" width="40" height="40">
                    <span>Rundad</span>
                </div>

                <div class="cart-style-option <?php echo $cart_style === 'modern' ? 'selected' : ''; ?>" data-value="modern">
                    <img src="<?php echo $plugin_url; ?>img/cart-modern.svg" alt="Modern" width="40" height="40">
                    <span>Modern</span>
                </div>

                <div class="cart-style-option <?php echo $cart_style === 'cart-outline' ? 'selected' : ''; ?>" data-value="cart-outline">
                    <img src="<?php echo $plugin_url; ?>img/cart-outline.svg" alt="Varukorg outline" width="40" height="40">
                    <span>Varukorg outline</span>
                </div>

                <div class="cart-style-option <?php echo $cart_style === 'bag-outline' ? 'selected' : ''; ?>" data-value="bag-outline">
                    <img src="<?php echo $plugin_url; ?>img/bag-outline.svg" alt="Väska outline" width="40" height="40">
                    <span>Väska outline</span>
                </div>

                <div class="cart-style-option <?php echo $cart_style === 'custom' ? 'selected' : ''; ?>" data-value="custom">
                    <span style="font-size: 24px;">+</span>
                    <span>Egen SVG</span>
                </div>
            </div>

            <script>
                jQuery(document).ready(function($) {
                    $('.cart-style-option').click(function() {
                        $('.cart-style-option').removeClass('selected');
                        $(this).addClass('selected');
                        $('#cart_style').val($(this).data('value'));

                        // Show/hide custom SVG field
                        if ($(this).data('value') === 'custom') {
                            $('.custom-svg-field').show();
                        } else {
                            $('.custom-svg-field').hide();
                        }
                    });

                    // Initial state
                    if ($('#cart_style').val() === 'custom') {
                        $('.custom-svg-field').show();
                    } else {
                        $('.custom-svg-field').hide();
                    }
                });
            </script>

            <p class="description">Välj stil på varukorgsikonen</p>
        </div>
        <?php
    }

    /**
     * Custom SVG callback
     */
    public function custom_svg_callback() {
        $custom_svg = isset($this->options['custom_svg']) ? $this->options['custom_svg'] : '';
        ?>
        <div class="custom-svg-field">
            <input type="text" id="custom_svg" name="smort_cart_options[custom_svg]" value="<?php echo esc_attr($custom_svg); ?>" class="regular-text" />
            <p class="description">Ange URL till din egen SVG-ikon eller använd media-biblioteket</p>
            <button type="button" class="button" id="upload_svg_button">Välj från media</button>
            <script>
                jQuery(document).ready(function($) {
                    $('#upload_svg_button').click(function(e) {
                        e.preventDefault();
                        var image = wp.media({
                            title: 'Ladda upp eller välj SVG',
                            multiple: false
                        }).open()
                        .on('select', function(e){
                            var uploaded_image = image.state().get('selection').first();
                            var image_url = uploaded_image.toJSON().url;
                            $('#custom_svg').val(image_url);
                        });
                    });
                });
            </script>
        </div>
        <?php
    }

    /**
     * Primary color callback
     */
    public function primary_color_callback() {
        $primary_color = isset($this->options['primary_color']) ? $this->options['primary_color'] : '#000000';
        ?>
        <input type="color" id="primary_color" name="smort_cart_options[primary_color]" value="<?php echo esc_attr($primary_color); ?>" />
        <p class="description">Välj primär färg för varukorgen (används för counter och accentfärg)</p>
        <?php
    }

    /**
     * Cart theme callback
     */
    public function cart_theme_callback() {
        $cart_theme = isset($this->options['cart_theme']) ? $this->options['cart_theme'] : 'modern';
        ?>
        <select name="smort_cart_options[cart_theme]" id="cart_theme">
            <option value="modern" <?php selected($cart_theme, 'modern'); ?>>Modern</option>
            <option value="clean" <?php selected($cart_theme, 'clean'); ?>>Clean</option>
            <option value="futuristic" <?php selected($cart_theme, 'futuristic'); ?>>Futuristisk</option>
        </select>
        <p class="description">Välj tema för varukorgens innehåll</p>
        <?php
    }

    /**
     * Header background color callback
     */
    public function header_bg_color_callback() {
        $header_bg_color = isset($this->options['header_bg_color']) ? $this->options['header_bg_color'] : '#ffffff';
        ?>
        <input type="color" id="header_bg_color" name="smort_cart_options[header_bg_color]" value="<?php echo esc_attr($header_bg_color); ?>" />
        <p class="description">Välj bakgrundsfärg för varukorgens header</p>
        <?php
    }

    /**
     * Header text color callback
     */
    public function header_text_color_callback() {
        $header_text_color = isset($this->options['header_text_color']) ? $this->options['header_text_color'] : '#000000';
        ?>
        <input type="color" id="header_text_color" name="smort_cart_options[header_text_color]" value="<?php echo esc_attr($header_text_color); ?>" />
        <p class="description">Välj textfärg för varukorgens header</p>
        <?php
    }

    /**
     * Button settings callback
     */
    public function button_settings_callback() {
        // Get button radius with special handling to ensure it's a valid number
        $button_radius = '0';
        if (isset($this->options['button_radius']) && is_numeric($this->options['button_radius'])) {
            $button_radius = $this->options['button_radius'];
        }

        $button_bg_color = isset($this->options['button_bg_color']) ? $this->options['button_bg_color'] : '#000000';
        $button_text_color = isset($this->options['button_text_color']) ? $this->options['button_text_color'] : '#ffffff';
        ?>
        <div class="button-settings">
            <div class="button-setting-field">
                <label for="button_radius">Border-radius:</label>
                <input type="number" id="button_radius" name="smort_cart_options[button_radius]" value="<?php echo esc_attr($button_radius); ?>" min="0" max="50" />
                <span>px</span>
            </div>

            <div class="button-setting-field" style="margin-top: 10px;">
                <label for="button_bg_color">Bakgrundsfärg:</label>
                <input type="color" id="button_bg_color" name="smort_cart_options[button_bg_color]" value="<?php echo esc_attr($button_bg_color); ?>" />
            </div>

            <div class="button-setting-field" style="margin-top: 10px;">
                <label for="button_text_color">Textfärg:</label>
                <input type="color" id="button_text_color" name="smort_cart_options[button_text_color]" value="<?php echo esc_attr($button_text_color); ?>" />
            </div>

            <p class="description">Anpassa utseendet på "Till kassan"-knappen</p>

            <script>
                jQuery(document).ready(function($) {
                    // Ensure the button_radius value is updated correctly
                    $('#button_radius').on('change', function() {
                        var value = $(this).val();
                        if (value === '' || isNaN(value)) {
                            $(this).val('0');
                        }
                    });
                });
            </script>
        </div>
        <?php
    }

    /**
     * Header logo callback
     */
    public function header_logo_callback() {
        $use_logo = isset($this->options['use_logo']) ? $this->options['use_logo'] : 'no';
        $header_logo = isset($this->options['header_logo']) ? $this->options['header_logo'] : '';
        ?>
        <div class="header-logo-field">
            <div>
                <label>
                    <input type="radio" name="smort_cart_options[use_logo]" value="no" <?php checked($use_logo, 'no'); ?> />
                    Visa text "Din varukorg"
                </label>
            </div>

            <div style="margin-top: 10px;">
                <label>
                    <input type="radio" name="smort_cart_options[use_logo]" value="yes" <?php checked($use_logo, 'yes'); ?> />
                    Visa logotyp
                </label>
            </div>

            <div class="logo-upload-field" style="margin-top: 10px; <?php echo $use_logo === 'yes' ? '' : 'display: none;'; ?>">
                <input type="text" id="header_logo" name="smort_cart_options[header_logo]" value="<?php echo esc_attr($header_logo); ?>" class="regular-text" />
                <button type="button" class="button" id="upload_logo_button">Välj från media</button>

                <?php if (!empty($header_logo)) : ?>
                <div class="logo-preview" style="margin-top: 10px;">
                    <img src="<?php echo esc_url($header_logo); ?>" alt="Logo preview" style="max-width: 200px; max-height: 50px;" />
                </div>
                <?php endif; ?>
            </div>

            <script>
                jQuery(document).ready(function($) {
                    // Toggle logo upload field
                    $('input[name="smort_cart_options[use_logo]"]').change(function() {
                        if ($(this).val() === 'yes') {
                            $('.logo-upload-field').show();
                        } else {
                            $('.logo-upload-field').hide();
                        }
                    });

                    // Media uploader
                    $('#upload_logo_button').click(function(e) {
                        e.preventDefault();
                        var image = wp.media({
                            title: 'Ladda upp eller välj logotyp',
                            multiple: false
                        }).open()
                        .on('select', function(e){
                            var uploaded_image = image.state().get('selection').first();
                            var image_url = uploaded_image.toJSON().url;
                            $('#header_logo').val(image_url);

                            // Update preview
                            if ($('.logo-preview').length) {
                                $('.logo-preview img').attr('src', image_url);
                            } else {
                                $('.logo-upload-field').append('<div class="logo-preview" style="margin-top: 10px;"><img src="' + image_url + '" alt="Logo preview" style="max-width: 200px; max-height: 50px;" /></div>');
                            }
                        });
                    });
                });
            </script>

            <p class="description">Välj om du vill visa text eller logotyp i varukorgens header</p>
        </div>
        <?php
    }

    /**
     * Notice settings callback
     */
    public function notice_settings_callback() {
        $notice_bg_color = isset($this->options['notice_bg_color']) ? $this->options['notice_bg_color'] : '#f7f6f7';
        $notice_text_color = isset($this->options['notice_text_color']) ? $this->options['notice_text_color'] : '#515151';
        $notice_border = isset($this->options['notice_border']) ? $this->options['notice_border'] : 'yes';
        ?>
        <div class="notice-settings">
            <div class="notice-setting-field">
                <label for="notice_bg_color">Bakgrundsfärg:</label>
                <input type="color" id="notice_bg_color" name="smort_cart_options[notice_bg_color]" value="<?php echo esc_attr($notice_bg_color); ?>" />
            </div>

            <div class="notice-setting-field" style="margin-top: 10px;">
                <label for="notice_text_color">Textfärg:</label>
                <input type="color" id="notice_text_color" name="smort_cart_options[notice_text_color]" value="<?php echo esc_attr($notice_text_color); ?>" />
            </div>

            <div class="notice-setting-field" style="margin-top: 10px;">
                <label>
                    <input type="radio" name="smort_cart_options[notice_border]" value="yes" <?php checked($notice_border, 'yes'); ?>>
                    Visa ram
                </label>
                <br>
                <label>
                    <input type="radio" name="smort_cart_options[notice_border]" value="no" <?php checked($notice_border, 'no'); ?>>
                    Dölj ram
                </label>
            </div>

            <div class="notice-preview" style="margin-top: 15px; padding: 10px; background-color: <?php echo esc_attr($notice_bg_color); ?>; color: <?php echo esc_attr($notice_text_color); ?>; <?php echo $notice_border === 'yes' ? 'border-left: 4px solid #00a32a;' : ''; ?>">
                <p style="margin: 0; padding: 0;">Förhandsvisning: "Produkt har lagts till i din varukorg."</p>
            </div>

            <script>
                jQuery(document).ready(function($) {
                    // Live preview for notice settings
                    $('#notice_bg_color, #notice_text_color').on('input', function() {
                        updateNoticePreview();
                    });

                    $('input[name="smort_cart_options[notice_border]"]').change(function() {
                        updateNoticePreview();
                    });

                    function updateNoticePreview() {
                        var bgColor = $('#notice_bg_color').val();
                        var textColor = $('#notice_text_color').val();
                        var showBorder = $('input[name="smort_cart_options[notice_border]"]:checked').val() === 'yes';

                        $('.notice-preview').css({
                            'background-color': bgColor,
                            'color': textColor,
                            'border-left': showBorder ? '4px solid #00a32a' : 'none'
                        });
                    }
                });
            </script>

            <p class="description">Anpassa utseendet på meddelanden som visas när produkter läggs till i varukorgen</p>
        </div>
        <?php
    }
    /**
     * Upsell settings callback
     */
    public function upsell_settings_callback() {
        $enable_upsells = isset($this->options['enable_upsells']) ? $this->options['enable_upsells'] : 'yes';
        $upsell_rules = isset($this->options['upsell_rules']) ? $this->options['upsell_rules'] : array();

        // Inkludera Select2 direkt i admin-sidan
        ?>
        <!-- Inkludera Select2 CSS och JS direkt -->
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
        <div class="upsell-settings">
            <div class="upsell-setting-field">
                <label>
                    <input type="radio" name="smort_cart_options[enable_upsells]" value="yes" <?php checked($enable_upsells, 'yes'); ?>>
                    Aktivera uppförsäljning
                </label>
                <br>
                <label>
                    <input type="radio" name="smort_cart_options[enable_upsells]" value="no" <?php checked($enable_upsells, 'no'); ?>>
                    Inaktivera uppförsäljning
                </label>
            </div>

            <div id="upsell-rules-container" style="margin-top: 20px; <?php echo $enable_upsells === 'no' ? 'display: none;' : ''; ?>">
                <h4>Uppförsäljningsregler</h4>
                <p class="description">Lägg till regler för att visa uppförsäljningsprodukter baserat på produkter i varukorgen.</p>

                <div id="upsell-rules">
                    <?php
                    if (!empty($upsell_rules)) {
                        foreach ($upsell_rules as $index => $rule) {
                            $this->render_upsell_rule($index, $rule);
                        }
                    }
                    ?>
                </div>

                <button type="button" class="button" id="add-product-rule">Lägg till produktregel</button>
                <button type="button" class="button" id="add-category-rule">Lägg till kategoriregel</button>

                <div style="margin-top: 20px; padding: 15px; background: #f5f5f5; border: 1px solid #ddd; border-radius: 4px;">
                    <h4>Felsökning</h4>
                    <p>Om du har problem med att lägga till regler, klicka på knappen nedan för att testa AJAX-anropen:</p>
                    <button type="button" class="button" id="test-ajax">Testa AJAX</button>
                    <div id="ajax-test-results" style="margin-top: 10px; padding: 10px; background: #fff; border: 1px solid #ddd; display: none;"></div>
                </div>

                <!-- Inline script för att säkerställa att knapparna fungerar -->
                <script type="text/javascript">
                jQuery(document).ready(function($) {
                    console.log('Inline script loaded');

                    // Check if Select2 is loaded, if not, try to load it
                    if (typeof $.fn.select2 !== 'function') {
                        console.warn('Select2 is not loaded, trying to load it dynamically...');

                        // Load Select2 dynamically
                        var select2Css = document.createElement('link');
                        select2Css.rel = 'stylesheet';
                        select2Css.href = 'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css';
                        document.head.appendChild(select2Css);

                        var select2Script = document.createElement('script');
                        select2Script.src = 'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js';
                        select2Script.onload = function() {
                            console.log('Select2 loaded dynamically!');
                            setTimeout(function() {
                                initializeSelect2();
                            }, 500);
                        };
                        document.head.appendChild(select2Script);
                    } else {
                        console.log('Select2 is already loaded');
                    }

                    // Function to add a new rule
                    function addRule(type) {
                        console.log('Adding new rule of type:', type);

                        try {
                            var index = $('#upsell-rules .upsell-rule').length;
                            var triggerLabel = type === 'product'
                                ? 'Om denna produkt finns i varukorgen:'
                                : 'Om en produkt från denna kategori finns i varukorgen:';
                            var triggerSelectClass = type === 'product' ? 'product-select' : 'category-select';
                            var triggerPlaceholder = type === 'product' ? 'Sök efter produkt...' : 'Sök efter kategori...';

                            var html = `
                                <div class="upsell-rule" style="margin-bottom: 15px; padding: 15px; background: #f9f9f9; border: 1px solid #ddd; border-radius: 4px;">
                                    <input type="hidden" name="smort_cart_options[upsell_rules][${index}][type]" value="${type}">

                                    <div style="margin-bottom: 15px;">
                                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">${triggerLabel}</label>
                                        <select name="smort_cart_options[upsell_rules][${index}][trigger_id]" class="${triggerSelectClass}" style="width: 100%;">
                                            <option value="">${triggerPlaceholder}</option>
                                        </select>
                                    </div>

                                    <div style="margin-bottom: 15px;">
                                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">Visa denna produkt som uppförsäljning:</label>
                                        <select name="smort_cart_options[upsell_rules][${index}][upsell_id]" class="product-select" style="width: 100%;">
                                            <option value="">Sök efter produkt...</option>
                                        </select>
                                    </div>

                                    <button type="button" class="button button-secondary remove-rule">Ta bort regel</button>
                                </div>
                            `;

                            $('#upsell-rules').append(html);
                            console.log('Rule HTML added to DOM');

                            // Initialize Select2 for the new elements with a delay
                            setTimeout(function() {
                                console.log('Initializing Select2 for new rule elements');
                                initializeSelect2();
                            }, 500);

                            return true;
                        } catch (e) {
                            console.error('Error adding rule:', e);
                            return false;
                        }
                    }

                    // Direct click handlers
                    $('#add-product-rule').on('click', function() {
                        console.log('Product rule button clicked (inline)');
                        addRule('product');
                    });

                    $('#add-category-rule').on('click', function() {
                        console.log('Category rule button clicked (inline)');
                        addRule('category');
                    });

                    // Test AJAX functionality
                    $('#test-ajax').on('click', function() {
                        console.log('Testing AJAX...');
                        $('#ajax-test-results').show().html('<p>Testar AJAX-anrop...</p>');

                        // Test product search
                        $.ajax({
                            url: ajaxurl,
                            type: 'GET',
                            dataType: 'json',
                            data: {
                                action: 'smort_cart_search_products',
                                term: 'test',
                                security: '<?php echo wp_create_nonce('smort_cart_search_products'); ?>'
                            },
                            success: function(response) {
                                console.log('Product search response:', response);
                                $('#ajax-test-results').append('<p>Produktsökning: ' + (response ? 'OK' : 'Fel') + '</p>');
                                $('#ajax-test-results').append('<p>Hittade ' + (response ? response.length : 0) + ' produkter</p>');

                                // Test category search
                                $.ajax({
                                    url: ajaxurl,
                                    type: 'GET',
                                    dataType: 'json',
                                    data: {
                                        action: 'smort_cart_search_categories',
                                        term: 'test',
                                        security: '<?php echo wp_create_nonce('smort_cart_search_categories'); ?>'
                                    },
                                    success: function(response) {
                                        console.log('Category search response:', response);
                                        $('#ajax-test-results').append('<p>Kategorisökning: ' + (response ? 'OK' : 'Fel') + '</p>');
                                        $('#ajax-test-results').append('<p>Hittade ' + (response ? response.length : 0) + ' kategorier</p>');

                                        // Test Select2
                                        $('#ajax-test-results').append('<p>Select2 status: ' + (typeof $.fn.select2 === 'function' ? 'Laddat' : 'Inte laddat') + '</p>');

                                        // Add a test select element
                                        $('#ajax-test-results').append('<p>Testar Select2-initiering:</p>');
                                        $('#ajax-test-results').append('<select id="test-select" style="width: 100%;"><option value="1">Test Option 1</option><option value="2">Test Option 2</option></select>');

                                        try {
                                            $('#test-select').select2();
                                            $('#ajax-test-results').append('<p>Select2-initiering: OK</p>');
                                        } catch (e) {
                                            $('#ajax-test-results').append('<p>Select2-initiering: Fel - ' + e.message + '</p>');
                                        }
                                    },
                                    error: function(xhr, status, error) {
                                        console.error('Category search error:', error);
                                        $('#ajax-test-results').append('<p>Kategorisökning: Fel - ' + error + '</p>');
                                    }
                                });
                            },
                            error: function(xhr, status, error) {
                                console.error('Product search error:', error);
                                $('#ajax-test-results').append('<p>Produktsökning: Fel - ' + error + '</p>');
                            }
                        });
                    });

                    // Remove rule
                    $(document).on('click', '.remove-rule', function() {
                        $(this).closest('.upsell-rule').remove();
                    });

                    // Initialize Select2
                    function initializeSelect2() {
                        console.log('Initializing Select2...');

                        // Check if Select2 is available
                        if (typeof $.fn.select2 !== 'function') {
                            console.error('Select2 is not loaded!');
                            return;
                        }

                        console.log('Select2 is available, initializing selects...');

                        // Initialize product selects
                        $('.product-select').each(function() {
                            var $select = $(this);
                            console.log('Initializing product select:', $select.attr('name'));

                            try {
                                if (!$select.hasClass('select2-hidden-accessible')) {
                                    $select.select2({
                                        ajax: {
                                            url: ajaxurl,
                                            dataType: 'json',
                                            delay: 250,
                                            data: function(params) {
                                                return {
                                                    action: 'smort_cart_search_products',
                                                    term: params.term,
                                                    security: '<?php echo wp_create_nonce('smort_cart_search_products'); ?>'
                                                };
                                            },
                                            processResults: function(data) {
                                                return {
                                                    results: data
                                                };
                                            },
                                            cache: true
                                        },
                                        minimumInputLength: 1,
                                        placeholder: 'Sök efter produkter',
                                        width: '100%'
                                    });
                                    console.log('Product select initialized successfully');
                                }
                            } catch (e) {
                                console.error('Error initializing product select:', e);
                            }
                        });

                        // Initialize category selects
                        $('.category-select').each(function() {
                            var $select = $(this);
                            console.log('Initializing category select:', $select.attr('name'));

                            try {
                                if (!$select.hasClass('select2-hidden-accessible')) {
                                    $select.select2({
                                        ajax: {
                                            url: ajaxurl,
                                            dataType: 'json',
                                            delay: 250,
                                            data: function(params) {
                                                return {
                                                    action: 'smort_cart_search_categories',
                                                    term: params.term,
                                                    security: '<?php echo wp_create_nonce('smort_cart_search_categories'); ?>'
                                                };
                                            },
                                            processResults: function(data) {
                                                return {
                                                    results: data
                                                };
                                            },
                                            cache: true
                                        },
                                        minimumInputLength: 1,
                                        placeholder: 'Sök efter kategorier',
                                        width: '100%'
                                    });
                                    console.log('Category select initialized successfully');
                                }
                            } catch (e) {
                                console.error('Error initializing category select:', e);
                            }
                        });
                    }

                    // Initialize Select2 on page load with a longer delay
                    setTimeout(function() {
                        console.log('Delayed initialization of Select2...');
                        initializeSelect2();
                    }, 1000);
                });
                </script>
            </div>
        </div>
        <?php
    }

    /**
     * AJAX handler for searching products
     */
    public function ajax_search_products() {
        // Debug info
        error_log('Smort Cart: Product search AJAX called');

        // Verify nonce - be more lenient for debugging
        if (!isset($_GET['security'])) {
            error_log('Smort Cart: No security token provided');
            // Continue anyway for debugging
        } else if (!wp_verify_nonce($_GET['security'], 'smort_cart_search_products')) {
            error_log('Smort Cart: Invalid security token');
            // Continue anyway for debugging
        }

        if (!current_user_can('manage_options')) {
            error_log('Smort Cart: Permission denied');
            wp_send_json_error('Permission denied');
            wp_die();
        }

        $term = isset($_GET['term']) ? sanitize_text_field($_GET['term']) : '';
        error_log('Smort Cart: Searching for term: ' . $term);

        if (empty($term)) {
            wp_send_json(array());
            wp_die();
        }

        // Make sure WooCommerce is active
        if (!function_exists('wc_get_products')) {
            error_log('Smort Cart: WooCommerce is not active');
            wp_send_json_error('WooCommerce is not active');
            wp_die();
        }

        // Fallback to direct database query if wc_get_products doesn't work well
        global $wpdb;

        $like_term = '%' . $wpdb->esc_like($term) . '%';

        // Search in post title
        $query = $wpdb->prepare(
            "SELECT ID, post_title FROM {$wpdb->posts}
            WHERE post_type IN ('product', 'product_variation')
            AND post_status = 'publish'
            AND post_title LIKE %s
            LIMIT 20",
            $like_term
        );

        $products = $wpdb->get_results($query);
        $results = array();

        if (!empty($products)) {
            foreach ($products as $product_data) {
                $product = wc_get_product($product_data->ID);
                if ($product && $product->is_purchasable()) {
                    $price_html = strip_tags(wc_price($product->get_price()));
                    $results[] = array(
                        'id' => $product->get_id(),
                        'text' => $product->get_name() . ' (' . $price_html . ')'
                    );
                }
            }
        }

        // If no results, try searching by SKU
        if (empty($results)) {
            $meta_query = $wpdb->prepare(
                "SELECT post_id FROM {$wpdb->postmeta}
                WHERE meta_key = '_sku'
                AND meta_value LIKE %s
                LIMIT 20",
                $like_term
            );

            $product_ids = $wpdb->get_col($meta_query);

            if (!empty($product_ids)) {
                foreach ($product_ids as $product_id) {
                    $product = wc_get_product($product_id);
                    if ($product && $product->is_purchasable()) {
                        $price_html = strip_tags(wc_price($product->get_price()));
                        $results[] = array(
                            'id' => $product->get_id(),
                            'text' => $product->get_name() . ' (' . $price_html . ')'
                        );
                    }
                }
            }
        }

        error_log('Smort Cart: Found ' . count($results) . ' products');
        wp_send_json($results);
        wp_die();
    }

    /**
     * AJAX handler for searching categories
     */
    public function ajax_search_categories() {
        // Debug info
        error_log('Smort Cart: Category search AJAX called');

        // Verify nonce - be more lenient for debugging
        if (!isset($_GET['security'])) {
            error_log('Smort Cart: No security token provided for category search');
            // Continue anyway for debugging
        } else if (!wp_verify_nonce($_GET['security'], 'smort_cart_search_categories')) {
            error_log('Smort Cart: Invalid security token for category search');
            // Continue anyway for debugging
        }

        if (!current_user_can('manage_options')) {
            error_log('Smort Cart: Permission denied for category search');
            wp_send_json_error('Permission denied');
            wp_die();
        }

        $term = isset($_GET['term']) ? sanitize_text_field($_GET['term']) : '';
        error_log('Smort Cart: Searching for category term: ' . $term);

        if (empty($term)) {
            wp_send_json(array());
            wp_die();
        }

        // Fallback to direct database query
        global $wpdb;

        $like_term = '%' . $wpdb->esc_like($term) . '%';

        // Search in term name
        $query = $wpdb->prepare(
            "SELECT t.term_id, t.name
            FROM {$wpdb->terms} AS t
            INNER JOIN {$wpdb->term_taxonomy} AS tt ON t.term_id = tt.term_id
            WHERE tt.taxonomy = 'product_cat'
            AND t.name LIKE %s
            LIMIT 20",
            $like_term
        );

        $categories = $wpdb->get_results($query);
        $results = array();

        if (!empty($categories)) {
            foreach ($categories as $category) {
                $results[] = array(
                    'id' => $category->term_id,
                    'text' => $category->name
                );
            }
        }

        // If no results, try searching by slug
        if (empty($results)) {
            $query = $wpdb->prepare(
                "SELECT t.term_id, t.name
                FROM {$wpdb->terms} AS t
                INNER JOIN {$wpdb->term_taxonomy} AS tt ON t.term_id = tt.term_id
                WHERE tt.taxonomy = 'product_cat'
                AND t.slug LIKE %s
                LIMIT 20",
                $like_term
            );

            $categories = $wpdb->get_results($query);

            if (!empty($categories)) {
                foreach ($categories as $category) {
                    $results[] = array(
                        'id' => $category->term_id,
                        'text' => $category->name
                    );
                }
            }
        }

        error_log('Smort Cart: Found ' . count($results) . ' categories');
        wp_send_json($results);
        wp_die();
    }

    /**
     * Render a single upsell rule
     */
    private function render_upsell_rule($index, $rule) {
        $type = isset($rule['type']) ? $rule['type'] : 'product';
        $trigger_id = isset($rule['trigger_id']) ? $rule['trigger_id'] : '';
        $upsell_id = isset($rule['upsell_id']) ? $rule['upsell_id'] : '';

        // Get product and category names
        $trigger_name = '';
        $upsell_name = '';
        $trigger_price = '';
        $upsell_price = '';

        if ($type === 'product' && !empty($trigger_id)) {
            $product = wc_get_product($trigger_id);
            if ($product) {
                $trigger_name = $product->get_name();
                $trigger_price = wc_price($product->get_price());
            }
        } elseif ($type === 'category' && !empty($trigger_id)) {
            $term = get_term($trigger_id, 'product_cat');
            if (!is_wp_error($term) && $term) {
                $trigger_name = $term->name;
            }
        }

        if (!empty($upsell_id)) {
            $product = wc_get_product($upsell_id);
            if ($product) {
                $upsell_name = $product->get_name();
                $upsell_price = wc_price($product->get_price());
            }
        }

        ?>
        <div class="upsell-rule" style="margin-bottom: 15px; padding: 15px; background: #f9f9f9; border: 1px solid #ddd; border-radius: 4px;">
            <input type="hidden" name="smort_cart_options[upsell_rules][<?php echo $index; ?>][type]" value="<?php echo esc_attr($type); ?>">

            <div style="margin-bottom: 15px;">
                <?php if ($type === 'product') : ?>
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">Om denna produkt finns i varukorgen:</label>
                    <select name="smort_cart_options[upsell_rules][<?php echo $index; ?>][trigger_id]" class="product-select" style="width: 100%;">
                        <?php if (!empty($trigger_id) && !empty($trigger_name)) : ?>
                            <option value="<?php echo esc_attr($trigger_id); ?>" selected><?php echo esc_html($trigger_name); ?> (<?php echo $trigger_price; ?>)</option>
                        <?php else : ?>
                            <option value="">Sök efter produkt...</option>
                        <?php endif; ?>
                    </select>
                <?php else : ?>
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">Om en produkt från denna kategori finns i varukorgen:</label>
                    <select name="smort_cart_options[upsell_rules][<?php echo $index; ?>][trigger_id]" class="category-select" style="width: 100%;">
                        <?php if (!empty($trigger_id) && !empty($trigger_name)) : ?>
                            <option value="<?php echo esc_attr($trigger_id); ?>" selected><?php echo esc_html($trigger_name); ?></option>
                        <?php else : ?>
                            <option value="">Sök efter kategori...</option>
                        <?php endif; ?>
                    </select>
                <?php endif; ?>
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 600;">Visa denna produkt som uppförsäljning:</label>
                <select name="smort_cart_options[upsell_rules][<?php echo $index; ?>][upsell_id]" class="product-select" style="width: 100%;">
                    <?php if (!empty($upsell_id) && !empty($upsell_name)) : ?>
                        <option value="<?php echo esc_attr($upsell_id); ?>" selected><?php echo esc_html($upsell_name); ?> (<?php echo $upsell_price; ?>)</option>
                    <?php else : ?>
                        <option value="">Sök efter produkt...</option>
                    <?php endif; ?>
                </select>
            </div>

            <button type="button" class="button button-secondary remove-rule">Ta bort regel</button>
        </div>
        <?php
    }
}

// Initialize the admin class
if (is_admin()) {
    $smort_cart_admin = new Smort_Cart_Admin();
}