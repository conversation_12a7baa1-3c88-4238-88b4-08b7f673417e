#smort-cart-container {
  position: relative;
  display: inline-block;
}

.smort-cart-icon {
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: relative;
  margin-right: 5px;
  margin-left: 5px;
}

.cart-icon {
  font-size: 24px;
  /* Color is now set via inline style */
}

.cart-count {
  position: absolute;
  top: 0;
  right: 0;
  /* Background color is now set via inline style */
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 10px;
}

.smort-cart-content {
  position: fixed;
  top: 0;
  right: -110%;
  width: 1000px;
  height: 100vh;
  background-color: #fafafa;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  overflow-y: auto;
  transition: right 0.3s ease;
  z-index: 10000;
}

@media (max-width: 1200px) {
  .smort-cart-content {
    width: 100%;
  }
}

.smort-cart-content.active {
  right: 0;
}

.cart-header {
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* Background color and text color are now set via inline style */
}

.cart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.header-logo {
  max-width: 200px;
  max-height: 50px;
  display: flex;
  align-items: center;
}

.header-logo img {
  max-width: 100%;
  max-height: 40px;
  object-fit: contain;
}

.close-cart {
  cursor: pointer;
  font-size: 24px; /* Reduced from 50px */
  /* Background color is now set via inline style */
  color: white;
  border-radius: 50%;
  width: 30px; /* Reduced from 40px */
  height: 30px; /* Reduced from 40px */
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem; /* Reduced from 1rem */
}

.cart-items {
  text-align: center;
}

.cart-items p {
  font-size: 1.7rem;
}

@media (max-width: 1200px) {
  .cart-items p {
    font-size: 18px;
  }
}

.cart-item {
  background: #ffff;
  display: flex;
  padding: 1.2rem 1rem;
  border-bottom: 1px solid #eee;
  align-items: flex-start;
  position: relative;
}

.item-image {
  margin-right: 10px;
}

.item-image img {
  width: 100px;
  height: 100px;
  object-fit: cover;
}

.item-details {
  flex-grow: 1;
  text-align: left;
  color: #333;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 100px;
}

.item-details h4 {
  margin: 0 0 2px 0;
  font-size: 15px;
  color: #333;
  line-height: 1.3;
}

.item-category {
  color: #999;
  margin: 3px 0 0 0;
  font-size: 11px;
  font-weight: normal;
}

.item-remove {
  position: absolute;
  top: 10px;
  right: 10px;
}

.item-remove a {
  /* Background color is now set via inline style */
  color: white;
  text-decoration: none;
  font-size: 12px; /* Reduced to 12px as requested */
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
}

.item-quantity-price {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 7px;
  width: 100%;
  padding-right: 20px; /* Add space for the remove button */
}

.quantity-controls {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f9f9f9;
}

.quantity-controls button.plus,
.quantity-controls button.minus {
  font-size: 16px;
}

.quantity-btn {
  width: 28px;
  height: 28px;
  background-color: #f0f0f0;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  transition: background-color 0.2s ease;
}

.quantity-btn:hover {
  background-color: #e0e0e0;
}

.quantity-input {
  width: 40px;
  height: 24px;
  text-align: center;
  border: none;
  border-radius: 3px;
  font-size: 14px;
  font-weight: 500;
  appearance: textfield;
  -moz-appearance: textfield; /* Firefox */
  padding: 0;
  margin: 0;
  line-height: 24px;
}

/* Remove arrows from number input */
.quantity-input::-webkit-outer-spin-button,
.quantity-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.item-price {
  font-weight: bold;
  margin: 0;
  text-align: right;
}

.item-price-container {
  min-width: 150px;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.price-calculation {
  white-space: nowrap;
  font-size: 12px;
  color: #666;
}

.cart-footer {
  padding: 0 15px 15px;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.cart-total {
  margin-bottom: 15px;
  margin-top: 5px;
}

.cart-footer a.checkout-btn {
  padding: 8px 15px;
  text-decoration: none;
  width: 97%;
  text-align: center;
  padding-top: 1.2rem;
  padding-bottom: 1.2rem;
  transition: all 0.3s ease;
  display: block;
}

.cart-footer a.checkout-btn:hover {
  opacity: 0.8;
}

.view-cart-btn {
  background-color: #f5f5f5;
  color: #333;
}

.checkout-btn {
  display: inline-block;
  /* Other styles are set via inline style */
}

.smort-cart-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.smort-cart-backdrop.active {
  display: block;
  opacity: 1;
}

.smort-cart-content {
  z-index: 10000;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
}

body.cart-open {
  overflow: hidden;
}

/* Upsell products */
.cart-upsells {
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
  margin-bottom: 10px;
  background-color: #f9f9f9;
}

.cart-upsells h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  text-align: left;
}

.upsell-products {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 0;
}

/* Ta bort scrollbar-styling eftersom vi inte längre har horisontell scrollning */

.upsell-product {
  flex: 0 0 auto;
  width: 100%;
  background: white;
  border: 1px solid #eee;
  border-radius: 5px;
  padding: 8px;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  overflow: hidden; /* Förhindra att innehåll sticker ut */
}

.upsell-product:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.upsell-product-info {
  display: flex;
  align-items: center;
  flex: 1;
  text-align: left;
  overflow: hidden;
  width: auto; /* Låt innehållet bestämma bredden */
  max-width: 70%; /* Begränsa bredden så att knappen får plats */
}

.upsell-product-details {
  margin-left: 10px;
  flex: 1;
  min-width: 0; /* För att hantera overflow korrekt */
  display: flex;
  flex-direction: column;
  justify-content: center;
  max-width: 40%; /* Begränsa bredden så att knappen får plats */
}

.upsell-product-actions {
  display: flex;
  align-items: center;
  margin-left: auto; /* Placera längst till höger */
  flex-shrink: 0; /* Förhindra att knappen krymper */
  width: auto; /* Låt innehållet bestämma bredden */
}

.upsell-product a {
  text-decoration: none;
  color: inherit;
}

.upsell-product-info > a {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-right: 5px;
}

.upsell-product img {
  width: 40px;
  height: 40px;
  border-radius: 3px;
  object-fit: cover;
}

.upsell-product h5 {
  margin: 0 0 2px 0;
  font-size: 13px;
  font-weight: 500;
  color: #333;
  line-height: 1.2;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
  text-align: left;
}

.upsell-product h5 a {
  display: inline;
  text-align: left;
}

.upsell-product .price {
  display: block;
  margin: 0;
  font-weight: bold;
  font-size: 12px;
  color: #333;
  white-space: nowrap;
  text-align: left;
}

.add-to-cart-btn {
  /* Background color will be set via inline style */
  color: white;
  border: none;
  padding: 5px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  min-width: 70px;
  font-weight: 500;
  transition: opacity 0.2s ease;
  white-space: nowrap;
  width: auto; /* Låt innehållet bestämma bredden */
}

.add-to-cart-btn:hover {
  opacity: 0.9;
}

@media (max-width: 768px) {
  .upsell-product {
    width: calc(50% - 5px);
  }
}

@media (max-width: 480px) {
  .upsell-product {
    width: 130px;
  }
}

/* Highlight animation for newly added items */
@keyframes highlight-pulse {
  0% { background-color: transparent; }
  50% { background-color: rgba(255, 255, 0, 0.2); }
  100% { background-color: transparent; }
}

.highlight-item {
  animation: highlight-pulse 1s ease-in-out 2;
}

/* Theme Styles */

/* Modern Theme */
.theme-modern .cart-item {
  border-radius: 8px;
  margin: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: none;
  transition: transform 0.2s ease;
}

.theme-modern .cart-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.theme-modern .item-details h4 {
  font-size: 16px;
  font-weight: 500;
}

.theme-modern .item-image img {
  border-radius: 6px;
}

.theme-modern .quantity-controls {
  border-radius: 20px;
  overflow: hidden;
  border: none;
  background-color: #f5f5f5;
}

.theme-modern .quantity-btn {
  background-color: transparent;
  border: none;
  /* Color is now set via inline style */
  font-weight: bold;
}

.theme-modern .checkout-btn {
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 14px;
}

/* Clean Theme */
.theme-clean .cart-item {
  border: none;
  border-bottom: 1px solid #eee;
  padding: 15px 10px;
}

.theme-clean .item-details h4 {
  font-size: 14px;
  font-weight: normal;
}

.theme-clean .item-image img {
  border: 1px solid #f0f0f0;
}

.theme-clean .quantity-controls {
  border: 1px solid #eee;
}

.theme-clean .quantity-btn {
  background-color: white;
  border: none;
}

.theme-clean .checkout-btn {
  text-transform: uppercase;
  font-size: 13px;
  letter-spacing: 1px;
  font-weight: normal;
}

/* Futuristic Theme */
.theme-futuristic .cart-header {
  /* Background is now set via inline style */
  background: linear-gradient(135deg, #ffffff 0%, rgba(0,0,0,0.1) 100%);
}

.theme-futuristic .cart-item {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  margin: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.theme-futuristic .item-details h4 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.theme-futuristic .item-image img {
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.theme-futuristic .quantity-controls {
  border: none;
  background: linear-gradient(to right, #f0f0f0, #f9f9f9);
  border-radius: 20px;
  padding: 2px;
}

.theme-futuristic .quantity-btn {
  background-color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  /* Color is now set via inline style */
}

.theme-futuristic .checkout-btn {
  /* Background is now set via inline style */
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.theme-futuristic .checkout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}
.woocommerce-message a.button.wc-forward {
  display: none;
}