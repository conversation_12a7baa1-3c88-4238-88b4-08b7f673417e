jQuery(document).ready(function($) {
    console.log('Admin script loaded');

    // Debug info
    console.log('Add product rule button:', $('#add-product-rule').length);
    console.log('Add category rule button:', $('#add-category-rule').length);
    // Initialize select2 for products
    function initProductSelects() {
        $('.product-select').each(function() {
            var $select = $(this);

            // Destroy if already initialized
            if ($select.hasClass('select2-hidden-accessible')) {
                $select.select2('destroy');
            }

            $select.select2({
                ajax: {
                    url: smort_cart_admin.ajax_url,
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            action: 'smort_cart_search_products',
                            term: params.term,
                            security: smort_cart_admin.product_nonce
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                minimumInputLength: 1,
                placeholder: '<PERSON>ök efter produkter',
                width: '100%',
                dropdownParent: $select.parent()
            });
        });
    }

    // Initialize select2 for categories
    function initCategorySelects() {
        $('.category-select').each(function() {
            var $select = $(this);

            // Destroy if already initialized
            if ($select.hasClass('select2-hidden-accessible')) {
                $select.select2('destroy');
            }

            $select.select2({
                ajax: {
                    url: smort_cart_admin.ajax_url,
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            action: 'smort_cart_search_categories',
                            term: params.term,
                            security: smort_cart_admin.category_nonce
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                minimumInputLength: 1,
                placeholder: 'Sök efter kategorier',
                width: '100%',
                dropdownParent: $select.parent()
            });
        });
    }

    // Toggle upsell rules container
    $('input[name="smort_cart_options[enable_upsells]"]').change(function() {
        if ($(this).val() === 'yes') {
            $('#upsell-rules-container').show();
        } else {
            $('#upsell-rules-container').hide();
        }
    });

    // Add product rule - use document for event delegation
    $(document).on('click', '#add-product-rule', function() {
        console.log('Add product rule clicked');
        var index = $('#upsell-rules .upsell-rule').length;
        var html = `
            <div class="upsell-rule" style="margin-bottom: 15px; padding: 15px; background: #f9f9f9; border: 1px solid #ddd; border-radius: 4px;">
                <input type="hidden" name="smort_cart_options[upsell_rules][${index}][type]" value="product">

                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">Om denna produkt finns i varukorgen:</label>
                    <select name="smort_cart_options[upsell_rules][${index}][trigger_id]" class="product-select" style="width: 100%;">
                        <option value="">Sök efter produkt...</option>
                    </select>
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">Visa denna produkt som uppförsäljning:</label>
                    <select name="smort_cart_options[upsell_rules][${index}][upsell_id]" class="product-select" style="width: 100%;">
                        <option value="">Sök efter produkt...</option>
                    </select>
                </div>

                <button type="button" class="button button-secondary remove-rule">Ta bort regel</button>
            </div>
        `;

        $('#upsell-rules').append(html);
        setTimeout(function() {
            initProductSelects();
        }, 10);
    });

    // Add category rule - use document for event delegation
    $(document).on('click', '#add-category-rule', function() {
        console.log('Add category rule clicked');
        var index = $('#upsell-rules .upsell-rule').length;
        var html = `
            <div class="upsell-rule" style="margin-bottom: 15px; padding: 15px; background: #f9f9f9; border: 1px solid #ddd; border-radius: 4px;">
                <input type="hidden" name="smort_cart_options[upsell_rules][${index}][type]" value="category">

                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">Om en produkt från denna kategori finns i varukorgen:</label>
                    <select name="smort_cart_options[upsell_rules][${index}][trigger_id]" class="category-select" style="width: 100%;">
                        <option value="">Sök efter kategori...</option>
                    </select>
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">Visa denna produkt som uppförsäljning:</label>
                    <select name="smort_cart_options[upsell_rules][${index}][upsell_id]" class="product-select" style="width: 100%;">
                        <option value="">Sök efter produkt...</option>
                    </select>
                </div>

                <button type="button" class="button button-secondary remove-rule">Ta bort regel</button>
            </div>
        `;

        $('#upsell-rules').append(html);
        setTimeout(function() {
            initProductSelects();
            initCategorySelects();
        }, 10);
    });

    // Remove rule
    $(document).on('click', '.remove-rule', function() {
        $(this).closest('.upsell-rule').remove();
        // Reindex rules
        $('#upsell-rules .upsell-rule').each(function(index) {
            $(this).find('input, select').each(function() {
                var name = $(this).attr('name');
                if (name) {
                    name = name.replace(/\[\d+\]/, '[' + index + ']');
                    $(this).attr('name', name);
                }
            });
        });
    });

    // Initialize existing selects
    setTimeout(function() {
        initProductSelects();
        initCategorySelects();
    }, 300);
});
