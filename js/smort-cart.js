jQuery(document).ready(function ($) {
  // Add backdrop element to the body
  $("body").append('<div class="smort-cart-backdrop"></div>');

  // Handle adding upsell products to cart
  $(document).on('click', '.add-upsell-to-cart', function() {
    var productId = $(this).data('product-id');
    var $button = $(this);

    $button.prop('disabled', true).text('Lägger till...');

    $.ajax({
      url: smort_cart_vars.ajax_url,
      type: 'POST',
      data: {
        action: 'add_upsell_to_cart',
        product_id: productId
      },
      success: function(response) {
        if (response.success) {
          // Update cart count
          $('.cart-count').text(response.data.cart_count);

          // Hide the upsell product
          $button.closest('.upsell-product').fadeOut(300, function() {
            $(this).remove();

            // If no more upsell products, hide the container
            if ($('.upsell-product').length === 0) {
              $('.cart-upsells').fadeOut(300, function() {
                $(this).remove();
              });
            }
          });

          // Refresh cart contents and show the new product
          $.ajax({
            url: smort_cart_vars.ajax_url,
            type: 'POST',
            data: {
              action: 'update_smort_cart',
              refresh_cart: true
            },
            success: function(html) {
              $('.cart-items').html(html);

              // Update total
              $.ajax({
                url: smort_cart_vars.ajax_url,
                type: 'POST',
                data: {
                  action: 'update_smort_cart',
                  get_total: true
                },
                success: function(html) {
                  $('.cart-total').html(html);

                  // Highlight the newly added product
                  if (response.data.cart_item_key) {
                    var $newItem = $('.cart-item[data-key="' + response.data.cart_item_key + '"]');
                    $newItem.addClass('highlight-item');
                    setTimeout(function() {
                      $newItem.removeClass('highlight-item');
                    }, 2000);
                  }
                }
              });
            }
          });
        } else {
          $button.prop('disabled', false).text('Lägg till');
          alert('Ett fel uppstod. Försök igen.');
        }
      },
      error: function() {
        $button.prop('disabled', false).text('Lägg till');
        alert('Ett fel uppstod. Försök igen.');
      }
    });
  });

  // Toggle cart visibility when clicking the cart icon
  $(".smort-cart-icon").on("click", function (e) {
    e.stopPropagation();
    $(".smort-cart-content").toggleClass("active");
    $(".smort-cart-backdrop").toggleClass("active");
    $("body").toggleClass("cart-open");
  });

  // Close cart when clicking the close button
  $(".close-cart").on("click", function () {
    $(".smort-cart-content").removeClass("active");
    $(".smort-cart-backdrop").removeClass("active");
    $("body").removeClass("cart-open");
  });

  // Close cart when clicking the backdrop
  $(document).on("click", ".smort-cart-backdrop", function () {
    $(".smort-cart-content").removeClass("active");
    $(".smort-cart-backdrop").removeClass("active");
    $("body").removeClass("cart-open");
  });

  // Close cart when clicking outside
  $(document).on("click", function (event) {
    if (!$(event.target).closest("#smort-cart-container").length) {
      $(".smort-cart-content").removeClass("active");
    }
  });

  // Remove item from cart
  $(document).on("click", ".remove-item", function (e) {
    e.preventDefault();

    var key = $(this).data("key");
    var $item = $(this).closest(".cart-item");

    // AJAX call to remove item
    $.ajax({
      url: smort_cart_vars.ajax_url,
      type: "POST",
      data: {
        action: "update_smort_cart",
        cart_item_key: key,
        remove_item: true,
      },
      success: function (response) {
        // Update cart display
        $item.fadeOut(300, function () {
          $(this).remove();

          // Update cart count
          updateCartCount();

          // Check if cart is empty
          if ($(".cart-item").length === 0) {
            $(".cart-items").html("<p>Din varukorg är tom.</p>");
          }

          // Update cart total
          $.ajax({
            url: smort_cart_vars.ajax_url,
            type: "POST",
            data: {
              action: "update_smort_cart",
              get_total: true,
            },
            success: function (response) {
              $(".cart-total").html(response);
            },
          });
        });
      },
    });
  });

  // Function to update cart count
  function updateCartCount() {
    $.ajax({
      url: smort_cart_vars.ajax_url,
      type: "POST",
      data: {
        action: "update_smort_cart",
        get_count: true,
      },
      success: function (response) {
        $(".cart-count").text(response);
      },
    });
  }

  // Update cart when WooCommerce cart is updated
  $(document.body).on("added_to_cart removed_from_cart", function () {
    updateCartCount();

    // Refresh cart contents
    $.ajax({
      url: smort_cart_vars.ajax_url,
      type: "POST",
      data: {
        action: "update_smort_cart",
        refresh_cart: true,
      },
      success: function (response) {
        $(".cart-items").html(response);
      },
    });
  });

  // Quantity controls
  $(document).on("click", ".quantity-btn.plus", function () {
    var $input = $(this).siblings(".quantity-input");
    var currentVal = parseInt($input.val());
    $input.val(currentVal + 1).trigger("change");
  });

  $(document).on("click", ".quantity-btn.minus", function () {
    var $input = $(this).siblings(".quantity-input");
    var currentVal = parseInt($input.val());
    if (currentVal > 1) {
      $input.val(currentVal - 1).trigger("change");
    }
  });

  // Update quantity when input changes
  $(document).on("change", ".quantity-input", function () {
    var key = $(this).data("key");
    var quantity = parseInt($(this).val());

    // AJAX call to update quantity
    $.ajax({
      url: smort_cart_vars.ajax_url,
      type: "POST",
      data: {
        action: "update_smort_cart",
        cart_item_key: key,
        quantity: quantity,
      },
      success: function (response) {
        // Refresh cart contents
        $.ajax({
          url: smort_cart_vars.ajax_url,
          type: "POST",
          data: {
            action: "update_smort_cart",
            refresh_cart: true,
          },
          success: function (response) {
            $(".cart-items").html(response);

            // Update cart total
            $.ajax({
              url: smort_cart_vars.ajax_url,
              type: "POST",
              data: {
                action: "update_smort_cart",
                get_total: true,
              },
              success: function (response) {
                $(".cart-total").html(response);
                updateCartCount();
              },
            });
          },
        });
      },
    });
  });
});
